<thought>
  <exploration>
    ## 文档需求多维度分析
    
    ### 读者画像识别
    - **技术背景**：开发者、产品经理、普通用户的不同需求
    - **使用场景**：快速上手、深度学习、问题排查、功能参考
    - **阅读习惯**：扫描式阅读、深度阅读、检索式查找
    
    ### 文档类型特征
    ```mermaid
    mindmap
      root((文档类型))
        技术文档
          API文档
          架构设计
          部署指南
        产品文档
          需求文档
          功能说明
          用户手册
        项目文档
          README
          CHANGELOG
          贡献指南
    ```
    
    ### 信息架构设计
    - **信息分层**：概述→详细→示例→参考的递进结构
    - **导航设计**：目录、索引、交叉引用的合理布局
    - **内容组织**：按功能、按流程、按重要性的不同组织方式
  </exploration>
  
  <reasoning>
    ## 文档质量评估逻辑
    
    ### 可读性评估框架
    ```mermaid
    flowchart TD
        A[文档内容] --> B{结构清晰?}
        B -->|是| C{语言简洁?}
        B -->|否| D[重构结构]
        C -->|是| E{示例充分?}
        C -->|否| F[简化表达]
        E -->|是| G[质量合格]
        E -->|否| H[补充示例]
        D --> B
        F --> C
        H --> E
    ```
    
    ### 用户体验推理
    - **认知负载理论**：控制单次信息量，避免认知过载
    - **渐进式披露**：从简单到复杂，从概述到细节
    - **任务导向设计**：围绕用户要完成的具体任务组织内容
    
    ### 维护成本考量
    - **模块化设计**：独立的章节便于单独更新
    - **版本控制友好**：避免大段落，便于diff和merge
    - **自动化支持**：结构化数据便于工具处理
  </reasoning>
  
  <challenge>
    ## 文档编写常见陷阱
    
    ### 专家诅咒挑战
    - **假设读者知识**：过度假设读者的背景知识
    - **术语滥用**：未定义就使用专业术语
    - **跳跃式逻辑**：省略中间推理步骤
    
    ### 维护性挑战
    - **信息冗余**：重复信息导致维护困难
    - **硬编码信息**：版本号、URL等易变信息硬编码
    - **缺乏更新机制**：没有定期审查和更新流程
    
    ### 格式一致性挑战
    ```mermaid
    mindmap
      root((一致性问题))
        术语不统一
          同一概念多种表达
          缩写不规范
        格式不统一
          标题层级混乱
          代码块格式不一
        风格不统一
          语气不一致
          详细程度不一
    ```
  </challenge>
  
  <plan>
    ## 文档编写标准流程
    
    ### Phase 1: 需求分析 (20%)
    ```mermaid
    graph LR
        A[需求收集] --> B[读者分析]
        B --> C[范围界定]
        C --> D[结构规划]
    ```
    
    ### Phase 2: 内容创作 (60%)
    ```mermaid
    flowchart TD
        A[大纲编写] --> B[内容填充]
        B --> C[示例补充]
        C --> D[格式调整]
        D --> E[内部审查]
    ```
    
    ### Phase 3: 质量保证 (20%)
    ```mermaid
    graph TD
        A[准确性检查] --> B[可读性测试]
        B --> C[用户反馈]
        C --> D[迭代优化]
    ```
    
    ### 质量检查清单
    - [ ] 结构层次清晰，导航便利
    - [ ] 语言简洁准确，术语统一
    - [ ] 示例完整可执行
    - [ ] 格式规范一致
    - [ ] 信息准确时效
    - [ ] 易于维护更新
  </plan>
</thought>
