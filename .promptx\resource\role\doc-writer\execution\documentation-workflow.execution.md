<execution>
  <constraint>
    ## 文档编写客观限制
    - **Markdown语法约束**：必须符合标准Markdown语法规范
    - **中文排版约束**：遵循中文技术文档排版规范
    - **平台兼容性**：确保在GitHub、GitLab等平台正确显示
    - **文件大小限制**：单个文档文件不超过100KB，便于加载和编辑
    - **图片资源约束**：图片需要合理压缩，支持多种格式
  </constraint>

  <rule>
    ## 文档编写强制规则
    - **标题层级规范**：严格使用H1-H6层级，不跳级使用
    - **代码块标注**：所有代码块必须标注语言类型
    - **链接有效性**：所有链接必须可访问，内部链接使用相对路径
    - **术语一致性**：项目内所有文档使用统一术语表
    - **更新时间标注**：重要文档必须标注最后更新时间
    - **目录结构**：超过3个章节的文档必须包含目录
  </rule>

  <guideline>
    ## 文档编写指导原则
    - **用户优先**：始终从用户角度思考文档结构和内容
    - **简洁明了**：用最少的文字表达最准确的信息
    - **示例驱动**：重要概念必须配备具体示例
    - **渐进式披露**：从简单到复杂，从概述到细节
    - **可操作性**：提供具体步骤，确保读者能够执行
    - **维护友好**：设计易于更新和维护的文档结构
  </guideline>

  <process>
    ## 文档编写标准流程
    
    ### Step 1: 需求分析与规划 (20分钟)
    
    ```mermaid
    flowchart TD
        A[收集需求] --> B[分析读者]
        B --> C[确定范围]
        C --> D[设计结构]
        D --> E[制定大纲]
    ```
    
    **具体操作**：
    1. **需求收集**：明确文档目的、目标读者、使用场景
    2. **读者分析**：确定读者的技术背景、知识水平、阅读习惯
    3. **范围界定**：确定文档覆盖的功能范围和详细程度
    4. **结构设计**：设计信息架构和导航结构
    5. **大纲制定**：列出主要章节和关键内容点
    
    ### Step 2: 内容创作与编写 (60分钟)
    
    ```mermaid
    graph LR
        A[框架搭建] --> B[内容填充]
        B --> C[示例补充]
        C --> D[格式优化]
    ```
    
    **标准模板结构**：
    ```markdown
    # 文档标题
    
    ## 概述
    - 简要说明
    - 适用场景
    - 前置条件
    
    ## 快速开始
    - 最简示例
    - 核心步骤
    
    ## 详细说明
    - 功能详解
    - 参数说明
    - 配置选项
    
    ## 示例集合
    - 常见用例
    - 最佳实践
    
    ## 故障排除
    - 常见问题
    - 解决方案
    
    ## 参考资料
    - 相关链接
    - 扩展阅读
    ```
    
    ### Step 3: 质量检查与优化 (20分钟)
    
    ```mermaid
    flowchart TD
        A[内容审查] --> B{质量检查}
        B -->|通过| C[发布文档]
        B -->|不通过| D[修改优化]
        D --> A
        C --> E[收集反馈]
        E --> F[持续改进]
    ```
    
    **质量检查清单**：
    - [ ] **结构检查**：层次清晰，导航便利
    - [ ] **内容检查**：信息准确，逻辑连贯
    - [ ] **语言检查**：表达清晰，术语统一
    - [ ] **格式检查**：Markdown规范，样式一致
    - [ ] **示例检查**：代码可执行，结果正确
    - [ ] **链接检查**：所有链接可访问
    - [ ] **可读性检查**：符合目标读者水平
    
    ### 文档类型专用流程
    
    #### API文档编写
    ```mermaid
    graph TD
        A[接口梳理] --> B[参数整理]
        B --> C[示例编写]
        C --> D[错误码说明]
        D --> E[SDK示例]
    ```
    
    #### 用户手册编写
    ```mermaid
    graph TD
        A[功能清单] --> B[使用流程]
        B --> C[截图制作]
        C --> D[常见问题]
        D --> E[联系方式]
    ```
    
    #### README编写
    ```mermaid
    graph TD
        A[项目介绍] --> B[安装说明]
        B --> C[快速开始]
        C --> D[功能特性]
        D --> E[贡献指南]
    ```
  </process>

  <criteria>
    ## 文档质量评价标准
    
    ### 内容质量 (40%)
    - ✅ 信息准确完整，无错误遗漏
    - ✅ 逻辑清晰连贯，层次分明
    - ✅ 示例丰富实用，可直接使用
    - ✅ 覆盖完整场景，满足用户需求
    
    ### 可读性 (30%)
    - ✅ 语言简洁明了，易于理解
    - ✅ 结构清晰合理，导航便利
    - ✅ 格式规范统一，视觉友好
    - ✅ 术语使用一致，定义明确
    
    ### 实用性 (20%)
    - ✅ 操作步骤具体，可直接执行
    - ✅ 问题解决方案有效
    - ✅ 适合目标读者水平
    - ✅ 满足实际使用场景
    
    ### 维护性 (10%)
    - ✅ 结构模块化，便于更新
    - ✅ 版本控制友好
    - ✅ 自动化工具支持
    - ✅ 更新机制完善
  </criteria>
</execution>
