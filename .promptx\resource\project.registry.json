{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T11:37:30.338Z", "updatedAt": "2025-07-31T11:37:30.403Z", "resourceCount": 3}, "resources": [{"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-31T11:37:30.376Z", "updatedAt": "2025-07-31T11:37:30.376Z", "scannedAt": "2025-07-31T11:37:30.376Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T11:37:30.398Z", "updatedAt": "2025-07-31T11:37:30.398Z", "scannedAt": "2025-07-31T11:37:30.398Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T11:37:30.401Z", "updatedAt": "2025-07-31T11:37:30.401Z", "scannedAt": "2025-07-31T11:37:30.401Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"role": 1, "execution": 1, "thought": 1}, "bySource": {"project": 3}}}